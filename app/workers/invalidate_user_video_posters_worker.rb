# frozen_string_literal: true

class InvalidateUserVideoPostersWorker
  include Sidekiq::Worker

  sidekiq_options queue: :default, retry: 3

  def perform(user_id)
    user = User.find_by(id: user_id)
    return unless user

    Rails.logger.info("Starting video poster invalidation for user #{user_id}")

    # Invalidate all active user video frames for this user
    invalidated_frames_count = invalidate_user_video_frames(user)

    # Invalidate all active user video posters for this user
    invalidated_posters_count = invalidate_user_video_posters(user)

    Rails.logger.info("Completed video poster invalidation for user #{user_id}: " \
                      "#{invalidated_frames_count} frames, #{invalidated_posters_count} posters invalidated")

    # Trigger regeneration of new video frames with updated photos
    CreateUserVideoFramesWorker.perform_async(user_id)

  rescue StandardError => e
    Rails.logger.error("Failed to invalidate video posters for user #{user_id}: #{e.message}")
    Honeybadger.notify(e, context: { user_id: user_id })
    raise
  end

  private

  def invalidate_user_video_frames(user)
    # Mark all active user video frames as inactive
    count = UserVideoFrame.where(user: user, active: true).update_all(active: false)
    Rails.logger.info("Invalidated #{count} user video frames for user #{user.id}")
    count
  end

  def invalidate_user_video_posters(user)
    # Mark all active user video posters as inactive
    count = UserVideoPoster.where(user: user, active: true).update_all(active: false)
    Rails.logger.info("Invalidated #{count} user video posters for user #{user.id}")
    count
  end
end
