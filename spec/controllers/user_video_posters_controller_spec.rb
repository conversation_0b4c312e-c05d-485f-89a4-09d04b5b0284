require 'rails_helper'

RSpec.describe UserVideoPostersController, type: :request do
  let(:user) { FactoryBot.create(:user) }
  let(:token) { user.generate_jwt_token }
  let(:font) do
    Font.find_by(name_font: "Noto Sans Telugu", badge_font: "Noto Sans Telugu") ||
      FactoryBot.create(:font, name_font: "Noto Sans Telugu", badge_font: "Noto Sans Telugu")
  end
  let(:video_frame) { FactoryBot.create(:video_frame, font: font) }
  let(:user_video_frame) { FactoryBot.create(:user_video_frame, user: user, video_frame: video_frame) }
  let(:video) { FactoryBot.create(:video, user: user) }
  let(:video_creative) { FactoryBot.create(:video_creative, video: video) }

  let(:headers) do
    {
      'Authorization' => "Bearer #{token}",
      'X-App-Version' => '2404.15.00',
      'X-Api-Key' => Rails.application.credentials[:api_key]
    }
  end



  describe "GET #show" do
    let!(:video_poster) { FactoryBot.create(:user_video_poster, user: user, user_video_frame: user_video_frame, source_video: video, active: true) }

    context "with valid id and active poster" do
      it "returns the video poster" do
        get "/video-posters/#{video_poster.id}", headers: headers
        expect(response).to have_http_status(:ok)

        body = JSON.parse(response.body)
        expect(body['id']).to eq(video_poster.id)
        expect(body['status']).to eq(video_poster.status)
      end
    end

    context "with inactive video poster" do
      before { video_poster.update!(active: false) }

      it "returns not found" do
        get "/video-posters/#{video_poster.id}", headers: headers
        expect(response).to have_http_status(:not_found)
      end
    end

    context "with invalid id" do
      it "returns not found" do
        get "/video-posters/999999", headers: headers
        expect(response).to have_http_status(:not_found)
      end
    end

    context "when video poster belongs to different user" do
      let(:other_user) { FactoryBot.create(:user) }
      let(:other_user_video_frame_for_poster) { FactoryBot.create(:user_video_frame, user: other_user, video_frame: FactoryBot.create(:video_frame, font: font)) }
      let(:other_video_poster) { FactoryBot.create(:user_video_poster, user: other_user, user_video_frame: other_user_video_frame_for_poster, source_video: video) }

      it "returns not found" do
        get "/video-posters/#{other_video_poster.id}", headers: headers
        expect(response).to have_http_status(:not_found)
      end
    end
  end

  describe "POST #create" do
    context "with valid parameters" do
      it "creates a new video poster" do
        # Use fresh objects to avoid conflicts with other tests
        fresh_video = FactoryBot.create(:video, user: user)
        fresh_video_creative = FactoryBot.create(:video_creative, video: fresh_video)
        fresh_user_video_frame = FactoryBot.create(:user_video_frame, user: user, video_frame: FactoryBot.create(:video_frame, font: font))

        # Ensure no existing poster for this combination
        expect(UserVideoPoster.where(user_video_frame_id: fresh_user_video_frame.id, source_video_id: fresh_video_creative.video_id)).to be_empty

        expect {
          post '/video-posters/create', params: {
            video_frame_id: fresh_user_video_frame.video_frame_id,
            video_creative_id: fresh_video_creative.id
          }, headers: headers
        }.to change(UserVideoPoster, :count).by(1)

        expect(response).to have_http_status(:ok)
        body = JSON.parse(response.body)
        expect(body['status']).to eq('pending')
      end

      it "automatically generates job_id and sets default status" do
        fresh_video = FactoryBot.create(:video, user: user)
        fresh_video_creative = FactoryBot.create(:video_creative, video: fresh_video)
        fresh_user_video_frame = FactoryBot.create(:user_video_frame, user: user, video_frame: FactoryBot.create(:video_frame, font: font))

        post '/video-posters/create', params: {
          video_frame_id: fresh_user_video_frame.video_frame_id,
          video_creative_id: fresh_video_creative.id
        }, headers: headers

        expect(response).to have_http_status(:ok)

        video_poster = UserVideoPoster.last
        expect(video_poster.job_id).to be_present
        expect(video_poster.job_id).to match(/^[0-9A-HJKMNP-TV-Z]{26}$/) # ULID format
        expect(video_poster.status).to eq('pending') # Initial status before processing

        # Note: Current implementation doesn't include job_id in get_json response
        body = JSON.parse(response.body)
        expect(body['id']).to eq(video_poster.id)
      end

      it "returns existing video poster if not failed" do
        existing_poster = FactoryBot.create(:user_video_poster,
          user: user,
          user_video_frame: user_video_frame,
          source_video: video_creative.video,
          status: 'completed'
        )

        post '/video-posters/create', params: {
          video_frame_id: user_video_frame.video_frame_id,
          video_creative_id: video_creative.id
        }, headers: headers

        expect(response).to have_http_status(:ok)
        body = JSON.parse(response.body)
        expect(body['id']).to eq(existing_poster.id)
      end

      it "retries failed video posters by resetting to pending" do
        failed_poster = FactoryBot.create(:user_video_poster,
          user: user,
          user_video_frame: user_video_frame,
          source_video: video_creative.video,
          status: 'failed',
          active: true
        )

        original_job_id = failed_poster.job_id

        post '/video-posters/create', params: {
          video_frame_id: user_video_frame.video_frame_id,
          video_creative_id: video_creative.id
        }, headers: headers

        expect(response).to have_http_status(:ok)

        failed_poster.reload
        expect(failed_poster.status).to eq('pending')
        expect(failed_poster.job_id).to eq(original_job_id) # Job ID should remain the same

        body = JSON.parse(response.body)
        expect(body['id']).to eq(failed_poster.id)
        expect(body['status']).to eq('pending')
      end

      it "creates new video poster when existing one is inactive" do
        inactive_poster = FactoryBot.create(:user_video_poster,
          user: user,
          user_video_frame: user_video_frame,
          source_video: video_creative.video,
          status: 'completed',
          active: false
        )

        expect {
          post '/video-posters/create', params: {
            video_frame_id: user_video_frame.video_frame_id,
            video_creative_id: video_creative.id
          }, headers: headers
        }.to change(UserVideoPoster, :count).by(1)

        expect(response).to have_http_status(:ok)

        new_poster = UserVideoPoster.where(active: true).last
        expect(new_poster.id).not_to eq(inactive_poster.id)
        expect(new_poster.active).to be true
        expect(new_poster.status).to eq('pending')
      end
    end

    context "with missing video_frame_id" do
      it "returns bad request" do
        post '/video-posters/create', params: { video_creative_id: video_creative.id }, headers: headers
        expect(response).to have_http_status(:bad_request)
      end
    end

    context "with blank video_frame_id" do
      it "returns bad request" do
        post '/video-posters/create', params: { video_frame_id: '', video_creative_id: video_creative.id }, headers: headers
        expect(response).to have_http_status(:bad_request)
      end
    end

    context "with missing video_creative_id" do
      it "returns bad request" do
        post '/video-posters/create', params: { video_frame_id: user_video_frame.video_frame_id }, headers: headers
        expect(response).to have_http_status(:bad_request)
      end
    end

    context "with blank video_creative_id" do
      it "returns bad request" do
        post '/video-posters/create', params: { video_frame_id: user_video_frame.video_frame_id, video_creative_id: '' }, headers: headers
        expect(response).to have_http_status(:bad_request)
      end
    end

    context "when user_video_frame does not exist" do
      it "returns not found" do
        post '/video-posters/create', params: {
          video_frame_id: 999999,
          video_creative_id: video_creative.id
        }, headers: headers
        expect(response).to have_http_status(:not_found)
      end
    end

    context "when user_video_frame belongs to different user" do
      let(:other_user) { FactoryBot.create(:user) }
      let(:other_user_video_frame) { FactoryBot.create(:user_video_frame, user: other_user, video_frame: FactoryBot.create(:video_frame, font: font)) }

      it "returns not found" do
        post '/video-posters/create', params: {
          video_frame_id: other_user_video_frame.video_frame_id,
          video_creative_id: video_creative.id
        }, headers: headers
        expect(response).to have_http_status(:not_found)
      end
    end

    context "when video_creative does not exist" do
      it "returns not found" do
        post '/video-posters/create', params: {
          video_frame_id: user_video_frame.video_frame_id,
          video_creative_id: 999999
        }, headers: headers
        expect(response).to have_http_status(:not_found)
      end
    end
  end

  describe "POST #video_poster_generation_failed" do
    let!(:video_poster) { FactoryBot.create(:user_video_poster, user: user, user_video_frame: user_video_frame, source_video: video, status: 'processing') }

    context "with valid job_id" do
      it "marks video poster as failed" do
        put '/video-posters/generation-failed', params: { job_id: video_poster.job_id, error_code: 'test_error' }, headers: headers
        expect(response).to have_http_status(:ok)

        video_poster.reload
        expect(video_poster.status).to eq('failed')
        expect(video_poster.error_code).to eq('test_error')
      end
    end

    context "with missing job_id" do
      it "returns bad request" do
        put '/video-posters/generation-failed', params: { error_code: 'test_error' }, headers: headers
        expect(response).to have_http_status(:bad_request)
      end
    end

    context "with blank job_id" do
      it "returns bad request" do
        put '/video-posters/generation-failed', params: { job_id: '', error_code: 'test_error' }, headers: headers
        expect(response).to have_http_status(:bad_request)
      end
    end

    context "when video poster does not exist" do
      it "returns not found" do
        put '/video-posters/generation-failed', params: { job_id: 'non_existent_job_id', error_code: 'test_error' }, headers: headers
        expect(response).to have_http_status(:not_found)
      end
    end

    context "when video poster cannot be failed" do
      let(:other_video) { FactoryBot.create(:video, user: user) }
      let(:other_user_video_frame) { FactoryBot.create(:user_video_frame, user: user, video_frame: FactoryBot.create(:video_frame, font: font)) }
      let!(:completed_poster) { FactoryBot.create(:user_video_poster, user: user, user_video_frame: other_user_video_frame, source_video: other_video, status: 'completed') }

      it "returns unprocessable entity" do
        put '/video-posters/generation-failed', params: { job_id: completed_poster.job_id, error_code: 'test_error' }, headers: headers
        expect(response).to have_http_status(:unprocessable_entity)
      end
    end
  end

  describe "POST #video_poster_generation_completed" do
    let!(:video_poster) { FactoryBot.create(:user_video_poster, user: user, user_video_frame: user_video_frame, source_video: video, status: 'processing') }
    let(:valid_params) do
      {
        job_id: video_poster.job_id,
        video_url: "https://praja-raw-user-videos.s3.ap-south-1.amazonaws.com/01JWTAKJYEDMNN09J8BR4Q39JD-video.mp4",
        thumbnail_url: "https://circle-app-photos.s3.ap-south-1.amazonaws.com/01JWTAKJYEDMNN09J8BR4Q39JD-thumbnail.jpg",
        width: 1080,
        height: 1420,
        duration: 78.84,
        bitrate: 465304
      }
    end

    context "with valid parameters" do
      it "marks video poster as completed and creates generated video with callback data" do
        put '/video-posters/generation-completed', params: valid_params, headers: headers
        expect(response).to have_http_status(:ok)

        video_poster.reload
        expect(video_poster.status).to eq('completed')
        expect(video_poster.generated_video).to be_present
        # Current implementation uses default URL pattern instead of callback URLs
        expect(video_poster.generated_video.url).to include(video_poster.job_id)
        expect(video_poster.generated_video.thumbnail_url).to include(video_poster.job_id)
        expect(video_poster.generated_video.width).to eq(valid_params[:width])
        expect(video_poster.generated_video.height).to eq(valid_params[:height])
        expect(video_poster.generated_video.duration).to eq(valid_params[:duration].to_i)
        expect(video_poster.generated_video.bitrate).to eq(valid_params[:bitrate])
      end
    end

    context "with missing job_id" do
      it "returns bad request" do
        put '/video-posters/generation-completed', headers: headers
        expect(response).to have_http_status(:bad_request)
      end
    end

    context "with blank job_id" do
      it "returns bad request" do
        put '/video-posters/generation-completed', params: { job_id: '' }, headers: headers
        expect(response).to have_http_status(:bad_request)
      end
    end

    context "with missing video_url" do
      it "returns bad request" do
        params = valid_params.except(:video_url)
        put '/video-posters/generation-completed', params: params, headers: headers
        expect(response).to have_http_status(:bad_request)
      end
    end

    context "with missing thumbnail_url" do
      it "returns bad request" do
        params = valid_params.except(:thumbnail_url)
        put '/video-posters/generation-completed', params: params, headers: headers
        expect(response).to have_http_status(:bad_request)
      end
    end

    context "when video poster does not exist" do
      it "returns not found" do
        params = valid_params.merge(job_id: 'non_existent_job_id')
        put '/video-posters/generation-completed', params: params, headers: headers
        expect(response).to have_http_status(:not_found)
      end
    end

    context "when video poster cannot be completed" do
      let(:other_video2) { FactoryBot.create(:video, user: user) }
      let(:other_user_video_frame2) { FactoryBot.create(:user_video_frame, user: user, video_frame: FactoryBot.create(:video_frame, font: font)) }
      let!(:failed_poster) { FactoryBot.create(:user_video_poster, user: user, user_video_frame: other_user_video_frame2, source_video: other_video2, status: 'failed') }

      it "returns unprocessable entity" do
        params = valid_params.merge(job_id: failed_poster.job_id)
        put '/video-posters/generation-completed', params: params, headers: headers
        expect(response).to have_http_status(:unprocessable_entity)
      end
    end
  end
end
