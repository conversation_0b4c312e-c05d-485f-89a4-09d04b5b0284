# frozen_string_literal: true

require 'rails_helper'

RSpec.describe InvalidateUserVideoPostersWorker, type: :worker do
  let(:user) { FactoryBot.create(:user) }
  let(:font) { Font.find_or_create_by(name_font: 'Noto Sans Telugu', badge_font: 'Noto Sans Telugu') }
  let(:video_frame1) { VideoFrame.create!(video_type: 'PORTRAIT', font: font, active: true) }
  let(:video_frame2) { VideoFrame.create!(video_type: 'SQUARE', font: font, active: true) }
  let(:video) { FactoryBot.create(:video, user: user) }

  describe '#perform' do
    context 'when user exists' do
      before do
        # Create test data directly to avoid factory constraint issues
        @user_video_frame1 = UserVideoFrame.create!(user: user, video_frame: video_frame1, identity_photo_url: 'https://example.com/photo1.jpg', active: true)
        @user_video_frame2 = UserVideoFrame.create!(user: user, video_frame: video_frame2, identity_photo_url: 'https://example.com/photo2.jpg', active: true)
        @user_video_poster1 = UserVideoPoster.create!(user: user, user_video_frame: @user_video_frame1, source_video: video, active: true, job_id: 'test_job_1', status: 'pending')
        @user_video_poster2 = UserVideoPoster.create!(user: user, user_video_frame: @user_video_frame2, source_video: video, active: true, job_id: 'test_job_2', status: 'pending')
      end

      let(:other_user) { FactoryBot.create(:user) }
      let(:video_frame3) { VideoFrame.create!(video_type: 'LANDSCAPE', font: font, active: true) }
      let(:video_frame4) { VideoFrame.create!(video_type: 'PORTRAIT', font: font, active: true) }

      before do
        # Create some inactive records to ensure they're not affected
        @inactive_user_video_frame = UserVideoFrame.create!(user: user, video_frame: video_frame3, identity_photo_url: 'https://example.com/inactive.jpg', active: false)
        @inactive_user_video_poster = UserVideoPoster.create!(user: user, user_video_frame: @user_video_frame1, source_video: video, active: false, job_id: 'inactive_job', status: 'pending')

        # Create records for another user to ensure they're not affected
        @other_user_video_frame = UserVideoFrame.create!(user: other_user, video_frame: video_frame4, identity_photo_url: 'https://example.com/other.jpg', active: true)
        @other_user_video_poster = UserVideoPoster.create!(user: other_user, user_video_frame: @other_user_video_frame, source_video: video, active: true, job_id: 'other_job', status: 'pending')
      end

      it 'invalidates all active user video frames for the user' do
        expect {
          described_class.new.perform(user.id)
        }.to change { @user_video_frame1.reload.active }.from(true).to(false)
         .and change { @user_video_frame2.reload.active }.from(true).to(false)

        # Ensure inactive records remain unchanged
        expect(@inactive_user_video_frame.reload.active).to be false

        # Ensure other user's records are not affected
        expect(@other_user_video_frame.reload.active).to be true
      end

      it 'invalidates all active user video posters for the user' do
        expect {
          described_class.new.perform(user.id)
        }.to change { @user_video_poster1.reload.active }.from(true).to(false)
         .and change { @user_video_poster2.reload.active }.from(true).to(false)

        # Ensure inactive records remain unchanged
        expect(@inactive_user_video_poster.reload.active).to be false

        # Ensure other user's records are not affected
        expect(@other_user_video_poster.reload.active).to be true
      end

      it 'triggers CreateUserVideoFramesWorker to regenerate frames' do
        expect(CreateUserVideoFramesWorker).to receive(:perform_async).with(user.id)
        described_class.new.perform(user.id)
      end

      it 'logs the invalidation process' do
        expect(Rails.logger).to receive(:info).with("Starting video poster invalidation for user #{user.id}")
        expect(Rails.logger).to receive(:info).with("Invalidated 2 user video frames for user #{user.id}")
        expect(Rails.logger).to receive(:info).with("Invalidated 2 user video posters for user #{user.id}")
        expect(Rails.logger).to receive(:info).with("Completed video poster invalidation for user #{user.id}: 2 frames, 2 posters invalidated")

        described_class.new.perform(user.id)
      end
    end

    context 'when user does not exist' do
      it 'returns early without error' do
        expect {
          described_class.new.perform(999999)
        }.not_to raise_error
      end

      it 'does not trigger CreateUserVideoFramesWorker' do
        expect(CreateUserVideoFramesWorker).not_to receive(:perform_async)
        described_class.new.perform(999999)
      end
    end

    context 'when user has no video frames or posters' do
      it 'completes successfully with zero counts' do
        expect(Rails.logger).to receive(:info).with("Starting video poster invalidation for user #{user.id}")
        expect(Rails.logger).to receive(:info).with("Invalidated 0 user video frames for user #{user.id}")
        expect(Rails.logger).to receive(:info).with("Invalidated 0 user video posters for user #{user.id}")
        expect(Rails.logger).to receive(:info).with("Completed video poster invalidation for user #{user.id}: 0 frames, 0 posters invalidated")

        described_class.new.perform(user.id)
      end

      it 'still triggers CreateUserVideoFramesWorker' do
        expect(CreateUserVideoFramesWorker).to receive(:perform_async).with(user.id)
        described_class.new.perform(user.id)
      end
    end

    context 'when an error occurs' do
      before do
        allow(UserVideoFrame).to receive(:where).and_raise(StandardError.new('Database error'))
      end

      it 'logs the error and notifies Honeybadger' do
        expect(Rails.logger).to receive(:error).with("Failed to invalidate video posters for user #{user.id}: Database error")
        expect(Honeybadger).to receive(:notify).with(instance_of(StandardError), context: { user_id: user.id })

        expect {
          described_class.new.perform(user.id)
        }.to raise_error(StandardError, 'Database error')
      end
    end
  end
end
