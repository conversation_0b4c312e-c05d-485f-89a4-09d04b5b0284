# frozen_string_literal: true

require 'rails_helper'

RSpec.describe UserRole, 'Identity Regeneration Callbacks', type: :model do
  let(:user) { FactoryBot.create(:user) }
  let(:role) { FactoryBot.create(:role, has_badge: true) }
  let(:circle) { FactoryBot.create(:circle) }
  let(:purview_circle) { FactoryBot.create(:circle) }

  describe 'badge description change triggers' do
    describe '#trigger_identity_regeneration_on_badge_change' do
      context 'when creating a new primary badge role' do
        it 'triggers IdentityPhotoGenerationWorker' do
          expect(IdentityPhotoGenerationWorker).to receive(:perform_async).with(user.id)
          
          FactoryBot.create(:user_role,
            user: user,
            role: role,
            parent_circle: circle,
            purview_circle: purview_circle,
            primary_role: true,
            active: true,
            verification_status: 'verified'
          )
        end

        it 'logs the regeneration trigger' do
          expect(Rails.logger).to receive(:info).with("Triggering identity image regeneration for user #{user.id} due to badge description changes")
          
          FactoryBot.create(:user_role,
            user: user,
            role: role,
            parent_circle: circle,
            purview_circle: purview_circle,
            primary_role: true,
            active: true,
            verification_status: 'verified'
          )
        end
      end

      context 'when updating role_id of existing badge role' do
        let!(:user_role) do
          FactoryBot.create(:user_role,
            user: user,
            role: role,
            parent_circle: circle,
            primary_role: true,
            active: true,
            verification_status: 'verified'
          )
        end
        let(:new_role) { FactoryBot.create(:role, has_badge: true) }

        it 'triggers IdentityPhotoGenerationWorker' do
          expect(IdentityPhotoGenerationWorker).to receive(:perform_async).with(user.id)
          user_role.update!(role: new_role)
        end
      end

      context 'when updating parent_circle_id' do
        let!(:user_role) do
          FactoryBot.create(:user_role,
            user: user,
            role: role,
            parent_circle: circle,
            primary_role: true,
            active: true,
            verification_status: 'verified'
          )
        end
        let(:new_circle) { FactoryBot.create(:circle) }

        it 'triggers IdentityPhotoGenerationWorker' do
          expect(IdentityPhotoGenerationWorker).to receive(:perform_async).with(user.id)
          user_role.update!(parent_circle: new_circle)
        end
      end

      context 'when updating free_text' do
        let!(:user_role) do
          FactoryBot.create(:user_role,
            user: user,
            role: role,
            parent_circle: circle,
            primary_role: true,
            active: true,
            verification_status: 'verified'
          )
        end

        it 'triggers IdentityPhotoGenerationWorker' do
          expect(IdentityPhotoGenerationWorker).to receive(:perform_async).with(user.id)
          user_role.update!(free_text: 'Custom Badge Text')
        end
      end

      context 'when changing verification_status to verified' do
        let!(:user_role) do
          FactoryBot.create(:user_role,
            user: user,
            role: role,
            parent_circle: circle,
            primary_role: true,
            active: true,
            verification_status: 'unverified'
          )
        end

        it 'triggers IdentityPhotoGenerationWorker' do
          expect(IdentityPhotoGenerationWorker).to receive(:perform_async).with(user.id)
          user_role.update!(verification_status: 'verified')
        end
      end

      context 'when deactivating a badge role' do
        let!(:user_role) do
          FactoryBot.create(:user_role,
            user: user,
            role: role,
            parent_circle: circle,
            primary_role: true,
            active: true,
            verification_status: 'verified'
          )
        end

        it 'triggers IdentityPhotoGenerationWorker' do
          expect(IdentityPhotoGenerationWorker).to receive(:perform_async).with(user.id)
          user_role.update!(active: false)
        end
      end
    end

    describe '#should_regenerate_identity_for_badge_change?' do
      let!(:user_role) do
        FactoryBot.create(:user_role,
          user: user,
          role: role,
          parent_circle: circle,
          primary_role: true,
          active: true,
          verification_status: 'verified'
        )
      end

      context 'when role is active, primary, and verified' do
        it 'returns true for role_id changes' do
          new_role = FactoryBot.create(:role, has_badge: true)
          user_role.role = new_role
          user_role.save!
          expect(user_role.send(:should_regenerate_identity_for_badge_change?)).to be true
        end

        it 'returns true for parent_circle_id changes' do
          new_circle = FactoryBot.create(:circle)
          user_role.parent_circle = new_circle
          user_role.save!
          expect(user_role.send(:should_regenerate_identity_for_badge_change?)).to be true
        end

        it 'returns true for free_text changes' do
          user_role.free_text = 'New Badge Text'
          user_role.save!
          expect(user_role.send(:should_regenerate_identity_for_badge_change?)).to be true
        end
      end

      context 'when role is not primary' do
        before { user_role.update!(primary_role: false) }

        it 'returns false for changes' do
          new_role = FactoryBot.create(:role, has_badge: true)
          user_role.role = new_role
          user_role.save!
          expect(user_role.send(:should_regenerate_identity_for_badge_change?)).to be false
        end
      end

      context 'when role is not active' do
        before { user_role.update!(active: false) }

        it 'returns false for changes' do
          user_role.free_text = 'New Badge Text'
          user_role.save!
          expect(user_role.send(:should_regenerate_identity_for_badge_change?)).to be false
        end
      end

      context 'when role is not verified' do
        before { user_role.update!(verification_status: 'unverified') }

        it 'returns false for changes' do
          user_role.free_text = 'New Badge Text'
          user_role.save!
          expect(user_role.send(:should_regenerate_identity_for_badge_change?)).to be false
        end
      end
    end

    describe 'non-triggering scenarios' do
      let!(:user_role) do
        FactoryBot.create(:user_role,
          user: user,
          role: role,
          parent_circle: circle,
          primary_role: true,
          active: true,
          verification_status: 'verified'
        )
      end

      it 'does not trigger for non-badge affecting changes' do
        expect(IdentityPhotoGenerationWorker).not_to receive(:perform_async)
        user_role.update!(is_celebrated: true)
      end

      it 'does not trigger for secondary roles' do
        secondary_role = FactoryBot.create(:role, has_badge: true)
        expect(IdentityPhotoGenerationWorker).not_to receive(:perform_async)
        
        FactoryBot.create(:user_role,
          user: user,
          role: secondary_role,
          parent_circle: circle,
          primary_role: false,
          active: true,
          verification_status: 'verified'
        )
      end

      it 'does not trigger for unverified roles' do
        unverified_role = FactoryBot.create(:role, has_badge: true)
        expect(IdentityPhotoGenerationWorker).not_to receive(:perform_async)
        
        FactoryBot.create(:user_role,
          user: user,
          role: unverified_role,
          parent_circle: circle,
          primary_role: true,
          active: true,
          verification_status: 'unverified'
        )
      end
    end
  end

  describe 'integration with existing callbacks' do
    it 'does not interfere with existing after_commit callbacks' do
      # Test that existing callbacks still work
      expect_any_instance_of(UserRole).to receive(:update_other_primary_role)
      expect_any_instance_of(UserRole).to receive(:index_for_search)
      
      FactoryBot.create(:user_role,
        user: user,
        role: role,
        parent_circle: circle,
        primary_role: true,
        active: true,
        verification_status: 'verified'
      )
    end
  end
end
