# frozen_string_literal: true

require 'rails_helper'

RSpec.describe User, 'Identity Regeneration Callbacks', type: :model do
  let(:user) { FactoryBot.create(:user, name: 'Original Name') }

  describe 'identity image regeneration triggers' do
    describe '#trigger_identity_regeneration' do
      context 'when name changes' do
        it 'triggers IdentityPhotoGenerationWorker' do
          expect(IdentityPhotoGenerationWorker).to receive(:perform_async).with(user.id)
          user.update!(name: 'New Name')
        end

        it 'logs the regeneration trigger' do
          expect(Rails.logger).to receive(:info).with("Triggering identity image regeneration for user #{user.id} due to field changes")
          user.update!(name: 'New Name')
        end
      end

      context 'when name does not change' do
        it 'does not trigger IdentityPhotoGenerationWorker' do
          expect(IdentityPhotoGenerationWorker).not_to receive(:perform_async)
          user.update!(email: '<EMAIL>')
        end
      end

      context 'when other fields change' do
        it 'does not trigger IdentityPhotoGenerationWorker' do
          expect(IdentityPhotoGenerationWorker).not_to receive(:perform_async)
          user.update!(phone: 9876543210)
        end
      end
    end

    describe '#should_regenerate_identity_image?' do
      it 'returns true when name changes' do
        user.name = 'New Name'
        user.save!
        expect(user.should_regenerate_identity_image?).to be true
      end

      it 'returns false when name does not change' do
        user.email = '<EMAIL>'
        user.save!
        expect(user.should_regenerate_identity_image?).to be false
      end
    end
  end

  describe 'video poster invalidation triggers' do
    describe '#invalidate_video_posters_on_photo_change' do
      let(:photo) { FactoryBot.create(:photo, user: user) }
      let(:poster_photo) { FactoryBot.create(:photo, user: user) }

      context 'when poster_photo_id changes' do
        it 'triggers InvalidateUserVideoPostersWorker' do
          expect(InvalidateUserVideoPostersWorker).to receive(:perform_async).with(user.id)
          user.update!(poster_photo: poster_photo)
        end

        it 'logs the invalidation trigger' do
          expect(Rails.logger).to receive(:info).with("Invalidating video posters for user #{user.id} due to photo changes")
          user.update!(poster_photo: poster_photo)
        end
      end

      context 'when photo_id changes' do
        it 'triggers InvalidateUserVideoPostersWorker' do
          expect(InvalidateUserVideoPostersWorker).to receive(:perform_async).with(user.id)
          user.update!(photo: photo)
        end
      end

      context 'when poster_photo_with_background_id changes' do
        it 'triggers InvalidateUserVideoPostersWorker' do
          expect(InvalidateUserVideoPostersWorker).to receive(:perform_async).with(user.id)
          user.update!(poster_photo_with_background: poster_photo)
        end
      end

      context 'when hero_frame_photo_id changes' do
        it 'triggers InvalidateUserVideoPostersWorker' do
          expect(InvalidateUserVideoPostersWorker).to receive(:perform_async).with(user.id)
          user.update!(hero_frame_photo: poster_photo)
        end
      end

      context 'when family_frame_photo_id changes' do
        it 'triggers InvalidateUserVideoPostersWorker' do
          expect(InvalidateUserVideoPostersWorker).to receive(:perform_async).with(user.id)
          user.update!(family_frame_photo: poster_photo)
        end
      end

      context 'when no photo fields change' do
        it 'does not trigger InvalidateUserVideoPostersWorker' do
          expect(InvalidateUserVideoPostersWorker).not_to receive(:perform_async)
          user.update!(name: 'New Name')
        end
      end
    end

    describe '#should_invalidate_video_posters?' do
      let(:photo) { FactoryBot.create(:photo, user: user) }

      it 'returns true when poster_photo_id changes' do
        user.poster_photo = photo
        user.save!
        expect(user.should_invalidate_video_posters?).to be true
      end

      it 'returns true when photo_id changes' do
        user.photo = photo
        user.save!
        expect(user.should_invalidate_video_posters?).to be true
      end

      it 'returns true when poster_photo_with_background_id changes' do
        user.poster_photo_with_background = photo
        user.save!
        expect(user.should_invalidate_video_posters?).to be true
      end

      it 'returns true when hero_frame_photo_id changes' do
        user.hero_frame_photo = photo
        user.save!
        expect(user.should_invalidate_video_posters?).to be true
      end

      it 'returns true when family_frame_photo_id changes' do
        user.family_frame_photo = photo
        user.save!
        expect(user.should_invalidate_video_posters?).to be true
      end

      it 'returns false when no photo fields change' do
        user.name = 'New Name'
        user.save!
        expect(user.should_invalidate_video_posters?).to be false
      end
    end
  end

  describe 'integration with existing callbacks' do
    it 'does not interfere with existing after_update_commit callbacks' do
      # Test that existing callbacks still work
      expect(user).to receive(:notify_users)
      expect(user).to receive(:check_signed_up_user)
      
      user.update!(name: 'New Name')
    end
  end
end
